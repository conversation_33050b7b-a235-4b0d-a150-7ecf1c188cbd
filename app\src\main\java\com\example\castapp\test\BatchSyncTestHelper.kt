package com.example.castapp.test

import com.example.castapp.websocket.ControlMessage
import com.example.castapp.utils.AppLog

/**
 * 批量同步功能测试辅助类
 * 用于手动验证批量同步功能的正确性
 */
object BatchSyncTestHelper {

    /**
     * 测试批量同步消息创建
     */
    fun testBatchSyncMessageCreation(): <PERSON><PERSON><PERSON> {
        return try {
            AppLog.d("🔄 [测试] 开始测试批量同步消息创建")
            
            // 模拟窗口数据
            val windowsData = listOf(
                mapOf(
                    "connectionId" to "test_window_1",
                    "positionX" to 100f,
                    "positionY" to 200f,
                    "scaleFactor" to 1.5f,
                    "rotationAngle" to 45f,
                    "isVisible" to true,
                    "alpha" to 0.8f,
                    "cornerRadius" to 16f,
                    "isBorderEnabled" to true,
                    "borderColor" to 0xFF6B6B6B.toInt(),
                    "borderWidth" to 2f
                ),
                mapOf(
                    "connectionId" to "test_window_2",
                    "positionX" to 300f,
                    "positionY" to 400f,
                    "scaleFactor" to 2.0f,
                    "rotationAngle" to 90f,
                    "isVisible" to false,
                    "alpha" to 1.0f,
                    "isMirrored" to true,
                    "cropRectRatio" to mapOf(
                        "left" to 0.1f,
                        "top" to 0.2f,
                        "right" to 0.9f,
                        "bottom" to 0.8f
                    )
                )
            )
            
            // 创建批量同步消息
            val batchSyncMessage = ControlMessage.createBatchWindowSyncControl(
                connectionId = "test_connection",
                allWindowsData = windowsData
            )
            
            // 验证消息结构
            val isValid = validateBatchSyncMessage(batchSyncMessage, windowsData)
            
            if (isValid) {
                AppLog.d("🔄 [测试] 批量同步消息创建测试通过 ✅")
            } else {
                AppLog.e("🔄 [测试] 批量同步消息创建测试失败 ❌")
            }
            
            isValid
            
        } catch (e: Exception) {
            AppLog.e("🔄 [测试] 批量同步消息创建测试异常", e)
            false
        }
    }

    /**
     * 验证批量同步消息的正确性
     */
    private fun validateBatchSyncMessage(
        message: ControlMessage,
        expectedWindowsData: List<Map<String, Any>>
    ): Boolean {
        try {
            // 验证消息类型
            if (message.type != ControlMessage.TYPE_REMOTE_WINDOW_TRANSFORM_CONTROL) {
                AppLog.e("🔄 [验证] 消息类型错误: ${message.type}")
                return false
            }
            
            // 验证变换类型
            val transformType = message.data["transform_type"]
            if (transformType != "batch_sync") {
                AppLog.e("🔄 [验证] 变换类型错误: $transformType")
                return false
            }
            
            // 验证变换数据
            @Suppress("UNCHECKED_CAST")
            val transformData = message.data["transform_data"] as? Map<String, Any>
            if (transformData == null) {
                AppLog.e("🔄 [验证] 变换数据为空")
                return false
            }
            
            // 验证窗口数据
            @Suppress("UNCHECKED_CAST")
            val windowsData = transformData["windows_data"] as? List<Map<String, Any>>
            if (windowsData == null) {
                AppLog.e("🔄 [验证] 窗口数据为空")
                return false
            }
            
            // 验证窗口数量
            val syncCount = transformData["sync_count"] as? Int
            if (syncCount != expectedWindowsData.size || windowsData.size != expectedWindowsData.size) {
                AppLog.e("🔄 [验证] 窗口数量不匹配: 期望${expectedWindowsData.size}, 实际${windowsData.size}, 计数$syncCount")
                return false
            }
            
            // 验证每个窗口的数据
            for (i in windowsData.indices) {
                val actualWindow = windowsData[i]
                val expectedWindow = expectedWindowsData[i]
                
                if (!validateWindowData(actualWindow, expectedWindow)) {
                    AppLog.e("🔄 [验证] 窗口数据验证失败: 索引$i")
                    return false
                }
            }
            
            // 验证时间戳
            if (!message.data.containsKey("timestamp")) {
                AppLog.e("🔄 [验证] 缺少时间戳")
                return false
            }
            
            AppLog.d("🔄 [验证] 批量同步消息验证通过")
            return true
            
        } catch (e: Exception) {
            AppLog.e("🔄 [验证] 批量同步消息验证异常", e)
            return false
        }
    }

    /**
     * 验证单个窗口数据
     */
    private fun validateWindowData(
        actualWindow: Map<String, Any>,
        expectedWindow: Map<String, Any>
    ): Boolean {
        try {
            // 验证关键字段
            val keyFields = listOf("connectionId", "positionX", "positionY", "scaleFactor", "rotationAngle")
            
            for (field in keyFields) {
                if (actualWindow[field] != expectedWindow[field]) {
                    AppLog.e("🔄 [验证] 字段不匹配: $field, 期望${expectedWindow[field]}, 实际${actualWindow[field]}")
                    return false
                }
            }
            
            // 验证裁剪数据（如果存在）
            val expectedCrop = expectedWindow["cropRectRatio"]
            val actualCrop = actualWindow["cropRectRatio"]
            
            if (expectedCrop != null && actualCrop != null) {
                @Suppress("UNCHECKED_CAST")
                val expectedCropMap = expectedCrop as Map<String, Any>
                @Suppress("UNCHECKED_CAST")
                val actualCropMap = actualCrop as Map<String, Any>
                
                val cropFields = listOf("left", "top", "right", "bottom")
                for (field in cropFields) {
                    if (actualCropMap[field] != expectedCropMap[field]) {
                        AppLog.e("🔄 [验证] 裁剪字段不匹配: $field")
                        return false
                    }
                }
            }
            
            return true
            
        } catch (e: Exception) {
            AppLog.e("🔄 [验证] 窗口数据验证异常", e)
            return false
        }
    }

    /**
     * 运行所有测试
     */
    fun runAllTests(): Boolean {
        AppLog.d("🔄 [测试] 开始运行批量同步功能测试")
        
        val testResults = mutableListOf<Boolean>()
        
        // 测试1: 批量同步消息创建
        testResults.add(testBatchSyncMessageCreation())
        
        val allPassed = testResults.all { it }
        
        if (allPassed) {
            AppLog.d("🔄 [测试] 所有批量同步测试通过 ✅")
        } else {
            AppLog.e("🔄 [测试] 部分批量同步测试失败 ❌")
        }
        
        return allPassed
    }
}
